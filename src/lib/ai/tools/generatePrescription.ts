import { tool } from "ai";
import { z } from "zod";
import { medicinesDetailsSchema } from "./schema/medicines-details";
import { diagnosisDetailsSchema } from "./schema/diagnosis-details";

import { updateDiagnosis } from "@/lib/services/diagnosis";
import { updateMedicines } from "@/lib/services/medicines";
import { RequestOptions } from "@/types/RequestOption";

import { updateObservation } from "@/lib/services/observation";

export const generatePrescription = (options: RequestOptions) =>
  tool({
    description:
      "Create prescription for a bovine based on details but you need observation, diagnosis , medication and vet consultant type before generating. create a summary for this prescription. from all the details given it should be in text",
    parameters: z.object({
      observation: z
        .object({
          weight: z
            .number()
            .optional()
            .nullable()
            .describe("Weight of the bovine from observation"),

          temperature: z
            .number()
            .optional()
            .nullable()
            .describe("Temperature of the bovine from observation"),
          careCalendarId: z.string().describe("care calendar id is required"),
        })
        .describe("The observation of the bovine in parameter  "),

      diagnosis: diagnosisDetailsSchema,
      medication: medicinesDetailsSchema,
      advisory_notes: z
        .string()
        .describe("advisory notes for the prescription"),
      downlaodPDF: z.object({
        farmerId: z.string().describe("farmer id is required"),
        careCalendarId: z.string().describe("care calendar id is required"),
      }),
    }),

    execute: async ({
      // complaintDetails,
      observation,
      advisory_notes,
      diagnosis,
      medication,
      downlaodPDF,
    }) => {
      console.log(
        "generatePrescription",
        observation,
        medication,
        diagnosis,
        advisory_notes,
        downlaodPDF
      );
      if (!diagnosis) {
        return "Required Diagnosis details,  ask for diagnosis";
      }

      if (!medication) {
        return "Required Medication details,  ask for medication";
      }

      try {
        // clean up old stuff diagnosis and medicines,
        await updateDiagnosis(
          {
            careCalendarId: diagnosis.careCalendarId,
            diagnosis: [],
          },
          options
        );
        await updateMedicines(
          {
            careCalendarId: medication.careCalendarId,
            medicines: [],
            follow_up_required: medication.follow_up_required,
            follow_up_date: medication.follow_up_date,
            follow_up_date_gmt: medication.follow_up_date_gmt,
            vet_cosultant_type: medication.vet_cosultant_type,
            advisory_notes: advisory_notes,
          },
          options
        );
        console.log("clear");
        await updateDiagnosis(diagnosis, options);
        console.log("Updated Diagnosis");

        await updateMedicines(
          { ...medication, advisory_notes: advisory_notes },
          options
        );
        console.log("Updated Medication");
        const observationData = {
          ...observation,
          weight: observation.weight === undefined ? null : observation.weight,
          temperature:
            observation.temperature === undefined
              ? null
              : observation.temperature,
        };
        await updateObservation(observationData, options);
        console.log("Updated Observation");
        /* call generate prescription here with the required details 
      once completed and successfully generated return the pdf url 
      return {
        message: "Prescription completed and generated",
        url: "https://example.com/pdf",
      }
      */
        return {
          message: "Prescription completed and generated",
          data: downlaodPDF,
        };
        // return "success";
      } catch (err) {
        console.log("Error in updating diagnosis", err);
        return "Error in updating diagnosis";
      }
    },
  });
