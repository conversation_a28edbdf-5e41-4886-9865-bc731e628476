import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from "ai";
import { groq } from "@ai-sdk/groq";
import { mistral } from "@ai-sdk/mistral";
import { isTestEnvironment } from "../constants";
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from "./models.test";
import {anthropic} from '@ai-sdk/anthropic'

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        "chat-model": chatModel,
        "chat-model-reasoning": reasoningModel,
        "title-model": titleModel,
        "artifact-model": artifactModel,
        "summarize-model": chatModel,
      },
    })
  : customProvider({
      languageModels: {
        "chat-model": anthropic("claude-3-7-sonnet-20250219"),
        "chat-model-reasoning": wrapLanguageModel({
          model: mistral("mistral-large-latest"),
          middleware: extractReasoningMiddleware({ tagName: "think" }),
        }),
        "title-model": groq("meta-llama/llama-4-maverick-17b-128e-instruct"),
        "artifact-model": mistral("mistral-large-latest"),
        "summarize-model": groq(
          "meta-llama/llama-4-maverick-17b-128e-instruct"
        ),
      },
    });
